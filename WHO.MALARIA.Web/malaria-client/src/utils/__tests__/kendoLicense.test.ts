/**
 * Tests for Kendo UI License Configuration
 */

import { activateKendoLicense, isKendoLicenseActivated } from "../kendoLicense";

// Mock the kendo-licensing module
jest.mock("@progress/kendo-licensing", () => ({
  setScriptKey: jest.fn(),
}));

describe("Kendo License Configuration", () => {
  beforeEach(() => {
    // Clear any previous activation state
    delete (window as any).__kendoLicenseActivated;
    jest.clearAllMocks();
  });

  describe("activateKendoLicense", () => {
    it("should activate the license successfully", () => {
      const { setScriptKey } = require("@progress/kendo-licensing");

      activateKendoLicense();

      expect(setScriptKey).toHaveBeenCalledWith(
        expect.stringContaining("141j044b041h541j4i1d542e581e4i1i4g260i2k0f2e")
      );
      expect((window as any).__kendoLicenseActivated).toBe(true);
    });

    it("should not activate license multiple times", () => {
      const { setScriptKey } = require("@progress/kendo-licensing");

      // First activation
      activateKendoLicense();
      expect(setScriptKey).toHaveBeenCalledTimes(1);

      // Second activation should be skipped
      activateKendoLicense();
      expect(setScriptKey).toHaveBeenCalledTimes(1);
    });

    it("should handle license activation errors gracefully", () => {
      const { setScriptKey } = require("@progress/kendo-licensing");
      const consoleSpy = jest.spyOn(console, "error").mockImplementation();

      setScriptKey.mockImplementation(() => {
        throw new Error("License activation failed");
      });

      expect(() => activateKendoLicense()).not.toThrow();
      expect(consoleSpy).toHaveBeenCalledWith(
        "❌ Failed to activate Kendo UI license:",
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe("isKendoLicenseActivated", () => {
    it("should return false when license is not activated", () => {
      expect(isKendoLicenseActivated()).toBe(false);
    });

    it("should return true when license is activated", () => {
      activateKendoLicense();
      expect(isKendoLicenseActivated()).toBe(true);
    });
  });
});
