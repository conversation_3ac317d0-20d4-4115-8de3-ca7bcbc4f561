import React, { Suspense } from "react";
import ReactDOM from "react-dom/client";

import "bootstrap/dist/js/bootstrap.js";
import reportWebVitals from "./reportWebVitals";

import { Provider } from "react-redux";
import "bootstrap/dist/css/bootstrap.min.css";
import "flag-icon-css/css/flag-icon.min.css";

import "@progress/kendo-theme-material/dist/all.css";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import "./index.scss";

import App from "./App";
import "./i18n";
import store from "./redux/configureStore";

// KendoReact License Configuration
// Using the script-based approach for KendoReact licensing

// Create and inject the license script
const kendoLicenseKey = import.meta.env.VITE_KENDO_UI_LICENSE || "";
if (kendoLicenseKey) {
  // Create a script element to set the license key
  const script = document.createElement("script");
  script.textContent = `
    if (typeof KendoLicensing !== 'undefined' && KendoLicensing.setScriptKey) {
      KendoLicensing.setScriptKey("${kendoLicenseKey}");
      console.log("✅ KendoReact license key registered via script");
    } else {
      // Fallback: set up a function to be called when KendoLicensing is available
      window.kendoLicenseKey = "${kendoLicenseKey}";
      window.setKendoLicense = function() {
        if (typeof KendoLicensing !== 'undefined' && KendoLicensing.setScriptKey) {
          KendoLicensing.setScriptKey(window.kendoLicenseKey);
          console.log("✅ KendoReact license key registered via fallback");
          return true;
        }
        return false;
      };

      // Try to set license periodically until successful
      const checkInterval = setInterval(function() {
        if (window.setKendoLicense && window.setKendoLicense()) {
          clearInterval(checkInterval);
        }
      }, 100);

      // Clear interval after 10 seconds to avoid infinite checking
      setTimeout(function() {
        clearInterval(checkInterval);
      }, 10000);
    }
  `;

  // Insert the script before other scripts
  document.head.insertBefore(script, document.head.firstChild);
  console.log("✅ KendoReact license script injected");
} else {
  console.warn(
    "⚠️ KendoReact license key not found. Please set VITE_KENDO_UI_LICENSE environment variable."
  );
}

const LoadingMarkup = () => {
  return (
    <div className='py-4 text-center'>
      <h3>Loading..</h3>
    </div>
  );
};
const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement
);
root.render(
  <Suspense fallback={<LoadingMarkup />}>
    <Provider store={store}>
      <App />
    </Provider>
  </Suspense>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
