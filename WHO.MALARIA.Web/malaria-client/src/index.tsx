import React, { Suspense } from "react";
import ReactDOM from "react-dom/client";

import "bootstrap/dist/js/bootstrap.js";
import reportWebVitals from "./reportWebVitals";

import { Provider } from "react-redux";
import "bootstrap/dist/css/bootstrap.min.css";
import "flag-icon-css/css/flag-icon.min.css";

import "@progress/kendo-theme-material/dist/all.css";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import "./index.scss";

import App from "./App";
import "./i18n";
import store from "./redux/configureStore";

// KendoReact License Configuration
// Import the licensing module to ensure it's loaded
import "@progress/kendo-licensing";

// Declare global KendoLicensing interface for TypeScript
declare global {
  interface Window {
    KendoLicensing?: {
      setScriptKey: (key: string) => void;
    };
  }
}

// Register KendoReact license key from environment variable
const kendoLicenseKey = import.meta.env.VITE_KENDO_UI_LICENSE || "";
if (kendoLicenseKey) {
  // Wait for the DOM to be ready and KendoLicensing to be available
  const setLicense = () => {
    if (window.KendoLicensing && window.KendoLicensing.setScriptKey) {
      window.KendoLicensing.setScriptKey(kendoLicenseKey);
      console.log("✅ KendoReact license key registered successfully");
    } else {
      console.warn("⚠️ KendoLicensing not available, retrying...");
      // Retry after a short delay
      setTimeout(setLicense, 100);
    }
  };

  // Set license when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", setLicense);
  } else {
    setLicense();
  }
} else {
  console.warn(
    "⚠️ KendoReact license key not found. Please set VITE_KENDO_UI_LICENSE environment variable."
  );
}

const LoadingMarkup = () => {
  return (
    <div className='py-4 text-center'>
      <h3>Loading..</h3>
    </div>
  );
};
const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement
);
root.render(
  <Suspense fallback={<LoadingMarkup />}>
    <Provider store={store}>
      <App />
    </Provider>
  </Suspense>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
