# KendoReact License Configuration Guide

This document explains how to configure the KendoReact license for both local development and Azure deployment.

## Overview

The application uses KendoReact components which require a valid license key. The license is configured using environment variables to keep it secure and outside the codebase.

## Local Development Setup

### 1. Create Environment File

Copy the example environment file and add your license key:

```bash
cd WHO.MALARIA.Web/malaria-client
cp .env.example .env.local
```

### 2. Add Your License Key

Edit `.env.local` and replace the placeholder with your actual KendoReact license key:

```env
# KendoReact License Configuration
VITE_KENDO_UI_LICENSE=your_actual_license_key_here
```

### 3. Get Your License Key

- Visit [Telerik Account Portal](https://www.telerik.com/account/)
- Navigate to your KendoReact subscription
- Copy the license key

### 4. Verify Setup

Start the development server and check the browser console:

```bash
npm run dev
```

- ✅ **Success**: No license warnings in console
- ❌ **Error**: Warning message about missing license key

## Azure Deployment Setup

### 1. Set Environment Variable in Azure

1. Go to **Azure Portal** → Your App Service → **Configuration**
2. Under **Application settings**, add:
   - **Name**: `KENDO_UI_LICENSE`
   - **Value**: `your_actual_license_key_here`
3. Click **Save**

### 2. Pipeline Configuration

The Azure DevOps pipeline automatically maps the Azure environment variable to the Vite build:

```yaml
# Build frontend (React) with KendoReact license
- task: Npm@1
  displayName: "Build frontend"
  inputs:
    workingDir: "$(reactAppDir)"
    command: "custom"
    customCommand: "run build"
  env:
    VITE_KENDO_UI_LICENSE: $(KENDO_UI_LICENSE)
```

### 3. Verify Deployment

After deployment, check the application logs for license-related messages.

## How It Works

### Environment Variable Flow

1. **Local Development**:

   - Vite reads `VITE_KENDO_UI_LICENSE` from `.env.local`
   - License is registered in `src/index.tsx`

2. **Azure Deployment**:
   - Azure App Service provides `KENDO_UI_LICENSE` environment variable
   - Build pipeline maps it to `VITE_KENDO_UI_LICENSE`
   - License is embedded in the built application

### Code Implementation

The license registration happens in `src/index.tsx` using a script-based approach:

```typescript
// KendoReact License Configuration
// Using the script-based approach for KendoReact licensing

// Create and inject the license script
const kendoLicenseKey = import.meta.env.VITE_KENDO_UI_LICENSE || "";
if (kendoLicenseKey) {
  // Create a script element to set the license key
  const script = document.createElement("script");
  script.textContent = `
    if (typeof KendoLicensing !== 'undefined' && KendoLicensing.setScriptKey) {
      KendoLicensing.setScriptKey("${kendoLicenseKey}");
      console.log("✅ KendoReact license key registered via script");
    } else {
      // Fallback mechanism for delayed KendoLicensing availability
      // ... (fallback code)
    }
  `;

  // Insert the script before other scripts
  document.head.insertBefore(script, document.head.firstChild);
  console.log("✅ KendoReact license script injected");
}
```

## Security Notes

- ✅ **DO**: Use environment variables for license keys
- ✅ **DO**: Keep `.env.local` in `.gitignore`
- ✅ **DO**: Use Azure App Service environment variables for production
- ❌ **DON'T**: Commit license keys to version control
- ❌ **DON'T**: Hardcode license keys in source code

## Troubleshooting

### License Warning in Console

**Problem**: Console shows "KendoReact license key not found" warning

**Solutions**:

1. Check if `.env.local` exists and contains the license key
2. Verify the environment variable name is exactly `VITE_KENDO_UI_LICENSE`
3. Restart the development server after adding the environment variable

### License Not Working in Production

**Problem**: License warnings appear in production

**Solutions**:

1. Verify `KENDO_UI_LICENSE` is set in Azure App Service configuration
2. Check Azure DevOps pipeline logs for environment variable mapping
3. Ensure the build pipeline includes the `env:` section

### Invalid License Key

**Problem**: License key is rejected

**Solutions**:

1. Verify the license key is copied correctly (no extra spaces)
2. Check if the license is still valid and not expired
3. Ensure the license covers the KendoReact components being used

## Support

For license-related issues:

- Contact Telerik Support: [https://www.telerik.com/support](https://www.telerik.com/support)
- Check KendoReact documentation: [https://www.telerik.com/kendo-react-ui/](https://www.telerik.com/kendo-react-ui/)
