import { defineConfig } from "vitest/config";
import react from "@vitejs/plugin-react";
import { resolve } from "path";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true,
    sourcemapIgnoreList: false,
  },
  build: {
    outDir: "build",
    sourcemap: true, // Enable sourcemaps for debugging
    minify: "terser",
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        manualChunks: {
          // Split vendor chunks to reduce memory usage
          "react-vendor": ["react", "react-dom"],
          "mui-vendor": ["@mui/material", "@mui/icons-material"],
          "kendo-vendor": [
            "@progress/kendo-react-grid",
            "@progress/kendo-react-charts",
            "@progress/kendo-react-dropdowns",
            "@progress/kendo-react-inputs",
            "@progress/kendo-react-dateinputs",
          ],
          "router-vendor": ["react-router-dom"],
          "utils-vendor": ["axios", "redux", "react-redux", "redux-saga"],
        },
        // Optimize chunk naming
        chunkFileNames: "assets/js/[name]-[hash].js",
        entryFileNames: "assets/js/[name]-[hash].js",
        assetFileNames: "assets/[ext]/[name]-[hash].[ext]",
      },
      // Reduce memory usage for Rollup
      maxParallelFileOps: 1,
    },
    // Reduce memory usage
    target: "esnext",
    assetsInlineLimit: 4096,
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        // Remove automatic import since files already import mixins manually
      },
    },
  },
  define: {
    // Replace process.env with import.meta.env for Vite
    "process.env": "import.meta.env",
    global: "globalThis",
  },
  // Add Node.js polyfills for browser compatibility
  esbuild: {
    // Ensure proper handling of Node.js globals
    define: {
      global: "globalThis",
    },
  },
  test: {
    environment: "jsdom",
    globals: true,
    include: ["src/**/*.{test,spec}.{js,ts,jsx,tsx}"],
    setupFiles: [], // Add setup files here if needed
    coverage: {
      reporter: ["text", "json", "html"],
    },
  },
  optimizeDeps: {
    include: ["react", "react-dom"],
  },
});
