trigger:
  - contract

pool:
  vmImage: "ubuntu-latest"

variables:
  buildConfiguration: "Release"
  apiProject: "WHO.MALARIA.Web/WHO.MALARIA.Web.csproj"
  reactAppDir: "WHO.MALARIA.Web/malaria-client"
  apiPublishDir: "$(Build.ArtifactStagingDirectory)/api"

steps:
  # Install .NET 8 SDK
  - task: UseDotNet@2
    displayName: "Install .NET 8 SDK"
    inputs:
      packageType: "sdk"
      version: "8.x"
      includePreviewVersions: false

  # Restore .NET dependencies
  - task: DotNetCoreCLI@2
    displayName: "Restore .NET packages"
    inputs:
      command: "restore"
      projects: "$(apiProject)"

  # Build .NET Web API
  - task: Dot<PERSON><PERSON>oreCLI@2
    displayName: "Build .NET Web API"
    inputs:
      command: "build"
      projects: "$(apiProject)"
      arguments: "--configuration $(buildConfiguration)"

  # Create API publish directory
  - script: mkdir -p $(apiPublishDir)
    displayName: "Create API publish directory"

  # Publish .NET Web API
  - task: DotNetCoreCLI@2
    displayName: "Publish Web API"
    inputs:
      command: "publish"
      projects: "$(apiProject)"
      arguments: "--configuration $(buildConfiguration) --output $(apiPublishDir)"

  # Unzip the WHO.MALARIA.Web.zip file
  - task: Bash@3
    displayName: "Unzip WHO.MALARIA.Web.zip"
    inputs:
      targetType: "inline"
      script: |
        unzip $(apiPublishDir)/WHO.MALARIA.Web.zip -d $(Build.ArtifactStagingDirectory)/api/.
        echo "Contents of unzipped directory:"
        ls -lR $(Build.ArtifactStagingDirectory)/api/.

  # delete the WHO.MALARIA.Web.zip file
  - task: Bash@3
    displayName: "Delete WHO.MALARIA.Web.zip"
    inputs:
      targetType: "inline"
      script: |
        rm -f $(apiPublishDir)/api/WHO.MALARIA.Web.zip $(Build.ArtifactStagingDirectory)/api/.
        ls -l $(apiPublishDir)

  #frontend
  # Install Node.js
  - task: UseNode@1
    displayName: "Install Node.js"
    inputs:
      version: "22.x" # Using 22.x instead of specific version for better caching

  # Install frontend dependencies
  - task: Npm@1
    displayName: "Install frontend dependencies"
    inputs:
      workingDir: "$(reactAppDir)"
      command: "install" # Use ci for clean install instead of install

  # Build frontend (React) with KendoReact license
  - task: Npm@1
    displayName: "Build frontend"
    inputs:
      workingDir: "$(reactAppDir)"
      command: "custom"
      customCommand: "run build"
    env:
      VITE_KENDO_UI_LICENSE: $(KENDO_UI_LICENSE)

  # FIXED: Copy frontend build to API wwwroot
  - task: Bash@3
    displayName: "Copy React build to API"
    inputs:
      targetType: "inline"
      script: |
        # Create target directory if it doesn't exist
        mkdir -p $(apiPublishDir)/malaria-client/build

        # Copy with preserve permissions and recursive
        cp -r -p $(reactAppDir)/build/. $(apiPublishDir)/malaria-client/build/

        # Verify copy operation
        echo "Copied files:"
        ls -lR $(apiPublishDir)/malaria-client/build

  # Publish build artifacts
  - task: PublishBuildArtifacts@1
    displayName: "Publish Artifact"
    inputs:
      PathtoPublish: "$(Build.ArtifactStagingDirectory)"
      ArtifactName: "drop"
      publishLocation: "Container"
